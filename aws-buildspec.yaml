version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20

    commands:
      - npm config set @mainframe-peru:registry https://npm.pkg.github.com
      - npm config set //npm.pkg.github.com/:_authToken $GITHUB_TOKEN
      - npm ci

  pre_build:
    commands:
      #- npm run test
      - npm run build

  build:
    commands:
      # Copy the package.json and cd into the build folder
      - cp package.json build/package.json
      - cp package-lock.json build/package-lock.json
      - cd build

      # Prepare package for prod
      - npm ci --omit=dev
      - rm -f package-lock.json
      - aws s3 cp $JWT_KEYS_S3_URI .env
      - wget https://truststore.pki.rds.amazonaws.com/us-east-1/us-east-1-bundle.pem
      - cd ..
  post_build:
    commands:
      # Package source code to S3
      - >
        aws cloudformation package
        --template-file "aws-sam-template.yaml"
        --output-template-file "aws-sam-template-final.yaml"
        --s3-bucket "$ARTIFACT_S3_BUCKET"
        --s3-prefix "$ARTIFACT_S3_BUCKET_PREFIX"
        --kms-key-id "$CODEBUILD_KMS_KEY_ID"

artifacts:
  type: zip
  discard-paths: yes
  files:
    - aws-sam-template-final.yaml
