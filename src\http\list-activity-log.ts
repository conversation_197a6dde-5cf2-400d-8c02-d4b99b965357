import { Middleware } from "@koa/router";
import { sc } from "../services";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";

export const listActivityLogMiddleware: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  unknown
> = async ({ request, response }) => {
  try {
    const { clientId, influencerId, type, status } = request.query as {
      clientId?: string;
      influencerId?: string;
      type?: string;
      status?: string;
    };
 
    response.status = 200;
    response.body = {
      items: result.Items || [],
      count: result.Count || 0,
    };
  } catch (err) {
    console.error("listActivityLogMiddleware error:", err);
    response.status = 500;
    response.body = { error: "Internal server error" };
  }
};
