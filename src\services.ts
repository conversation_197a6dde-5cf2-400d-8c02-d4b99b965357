import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { SNSClient } from "@aws-sdk/client-sns";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";
import { config } from "dotenv";

class ServiceController {
  db: DynamoDBDocument;
  sns: SNSClient;

  constructor() {
    const isTest = !!process.env.JEST_WORKER_ID;
    this.sns = new SNSClient();
    this.db = DynamoDBDocument.from(
      isTest
        ? new DynamoDB({
            endpoint: "http://localhost:8000",
            region: "local-env",
            maxAttempts: 1,
            credentials: {
              accessKeyId: "fakeMyKeyId",
              secretAccessKey: "fakeSecretAccessKey",
            },
          })
        : new DynamoDB(),
    );

    // Load environment variables using dotenv
    config();
  }

  get vars() {
    return {
      env: (process.env.ENV_NAME || "prod") as "dev" | "stg" | "prod",
      activityLogTableName: process.env.ACTIVITY_LOG_TABLE_NAME || "",
      snsTopicArn: process.env.SNS_TOPIC_ARN || "",
      keys: {
        officer: {
          private: process.env.OFFICER_JWT_PRIVATE_KEY || "",
          public: process.env.OFFICER_JWT_PUBLIC_KEY || "",
        },
        admin: {
          private: process.env.ADMIN_JWT_PRIVATE_KEY || "",
          public: process.env.ADMIN_JWT_PUBLIC_KEY || "",
        },
        user: {
          private: process.env.USER_JWT_PRIVATE_KEY || "",
          public: process.env.USER_JWT_PUBLIC_KEY || "",
        },
      },
    };
  }
}

export const sc = new ServiceController();
