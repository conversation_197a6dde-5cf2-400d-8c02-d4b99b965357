import serverlessExpress from "@codegenie/serverless-express";
import { bodyParser } from "@koa/bodyparser";
import Router from "@koa/router";
import {
  buildSQSHandler,
  errorMiddleware,
  eventSource,
  loggerMiddleware,
} from "@mainframe-peru/common-core";
import Koa from "koa";
import { activityLogRouter } from "./http";
const koa = new Koa();
const router = new Router();

/**
 * Middlewares
 */
koa.use(errorMiddleware);
koa.use(bodyParser());
koa.use(loggerMiddleware);

/**
 * Setup routes
 */
router.use("/activity-log", activityLogRouter.routes());

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = buildSQSHandler(
  serverlessExpress({
    app,
    eventSource,
    eventSourceRoutes: {
      AWS_SQS: "/",
    },
  }),
);
