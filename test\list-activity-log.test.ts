import request from "supertest";
import { app } from "../src/api-handler";
import { sc } from "../src/services";
import { userAuthToken } from "./common";

const now = Date.now();
const old = now - 1000;

const TestData = {
  user: { id: 123 },
  influencer: { id: "infl-1" },
};

beforeAll(async () => {
  // Inserta entidades simuladas
  const items = [
    {
      pk: `u#${TestData.user.id}`,
      sk: `${old}#UPDATE_ACCOUNT#SUCCESS`,
      clientType: "u",
      clientId: TestData.user.id,
      influencerId: TestData.influencer.id,
      type: "UPDATE_ACCOUNT",
      status: "SUCCESS",
      message: "msg1",
      request: "{x:1}",
      createdAt: old,
      gsi1pk: TestData.influencer.id,
      gsi1sk: `${old}#UPDATE_ACCOUNT#SUCCESS`,
      gsi2pk: `u#${TestData.user.id}`,
      gsi2sk: `UPDATE_ACCOUNT#SUCCESS#${old}`,
    },
    {
      pk: `u#${TestData.user.id}`,
      sk: `${now}#LOGIN#SUCCESS`,
      clientType: "u",
      clientId: TestData.user.id,
      influencerId: TestData.influencer.id,
      type: "LOGIN",
      status: "SUCCESS",
      message: "msg2",
      request: "{x:2}",
      createdAt: now,
      gsi1pk: TestData.influencer.id,
      gsi1sk: `${now}#LOGIN#SUCCESS`,
      gsi2pk: `u#${TestData.user.id}`,
      gsi2sk: `LOGIN#SUCCESS#${now}`,
    },
  ];

  for (const item of items) {
    await sc.db.put({ TableName: sc.vars.activityLogTableName, Item: item });
  }
});

test("List activity logs by clientId", async () => {
  const response = await request(app)
    .get(`/live?clientId=${TestData.user.id}`)
    .set("Cookie", `session=${await userAuthToken}`);
  expect(response.status).toBe(200);
  expect(response.body.items.length).toBeGreaterThanOrEqual(2);
});

test("List activity logs by influencerId", async () => {
  const response = await request(app)
    .get(`/live?influencerId=${TestData.influencer.id}`)
    .set("Cookie", `session=${await userAuthToken}`);
  expect(response.status).toBe(200);
  expect(response.body.items.some((i: any) => i.type === "LOGIN")).toBe(true);
});

test("Filter activity logs by influencerId and type", async () => {
  const response = await request(app)
    .get(`/live?influencerId=${TestData.influencer.id}&type=LOGIN`)
    .set("Cookie", `session=${await userAuthToken}`);
  expect(response.status).toBe(200);
  expect(response.body.items).toHaveLength(1);
  expect(response.body.items[0].type).toBe("LOGIN");
});
