import { createJWT, Policies } from "@mainframe-peru/common-core";
import { sc } from "../src/services";
import { userPoliciesConstant } from "@mainframe-peru/common-core/build/policies/lib/modules/user";

const influencer = {
  id: "pchujoy",
  name: "Bizz name",
  status: "ACTIVE",
  logoUrl: "www",
  transientUsers: false,
  domain: "fcc.pchujoy.app",
  createdAt: new Date(),
  updatedAt: new Date(),
  attributes: null,
  emailsConfiguration: {
    templates: {
      contact: {
        type: "contact",
        subject: "Contacto desde Pchujoy",
        from: "<EMAIL>",
        cc: "<EMAIL>",
        bcc: "<EMAIL>",
        template: "contact",
      },
    },
  },
  providersConfiguration: {
    GOOGLE: {
      name: "Google",
      apiKey: "eyx",
    },
    CULQI: {
      name: "<PERSON><PERSON><PERSON>",
      apiKey: "eyx",
    },
  },
};

const user = {
  id: *********,
  influencerId: influencer.id,
  authenticationType: "EMAIL",
  email: "<EMAIL>",
  firstName: "<PERSON><PERSON><PERSON><PERSON>",
  lastName: "<PERSON><PERSON><PERSON>",
  alias: "OptimistaLima7",
  phone: "*********",
  gender: "M",
  createdAt: new Date(),
  updatedAt: new Date(),
  policies: {},
  documentType: "DNI",
  documentValue: "12345678",
  city: "Lima",
  country: "PE",
  line1: "test address 1",
  province: "Lima",
  birthDate: new Date(),
  companyId: "*********",
  district: "",
  hash: "",
  line2: "",
  zipCode: "123123",
  attributes: null,
  isPartner: false,
};

export const TestData = {
  influencer,
  user,
} as const;

export const userAuthToken = createJWT(
  "user",
  {
    id: TestData.user.id,
    influencerId: TestData.influencer.id,
    email: TestData.user.email,
    firstName: TestData.user.firstName || "",
    lastName: TestData.user.lastName || "",
    policies: Policies.mask(
      {
        general: {
          REGULAR: true,
          TRANSIENT: true,
        },
      },
      userPoliciesConstant,
    ),
  },
  sc.vars.keys.user.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);
