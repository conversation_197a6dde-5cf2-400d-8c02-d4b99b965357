AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: MS for saving clients' activity
Parameters:
  ServiceName:
    Type: String
    Default: activity-log

  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

Conditions:
  IsDev: !Equals [!Ref EnvName, "dev"]
  IsStg: !Equals [!Ref EnvName, "stg"]
  IsProd: !Equals [!Ref EnvName, "prod"]
  PrefixResources: !Or
    - !Equals [!Ref EnvName, "dev"]
    - !Equals [!Ref EnvName, "stg"]

Resources:
  #----------------------------------------------------------------
  # Lambda Role
  #----------------------------------------------------------------
  LambdaFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ServiceName}-lambdafnc-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaENIManagementAccess

  # Main policy, separated from the role so that they
  # can use dependant items without making loops
  LambdaFunctionRolePolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: MainPolicy
      Roles:
        - !Ref LambdaFunctionRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AccessDynamoDb
            Effect: Allow
            Action:
              - dynamodb:Batch*
              - dynamodb:Condition*
              - dynamodb:DeleteItem
              - dynamodb:Get*
              - dynamodb:Put*
              - dynamodb:Query
              - dynamodb:UpdateItem
              - dynamodb:Scan
            Resource:
              - "*"
          - Sid: AllowPushToSNS
            Effect: Allow
            Action: sns:Publish
            Resource:
              - !ImportValue MAIN-SNS-EVENT-BUS-ARN
          - Sid: AccessSQS
            Effect: Allow
            Action:
              - "sqs:ReceiveMessage"
              - "sqs:DeleteMessage"
              - "sqs:Send*"
              - "sqs:GetQueueAttributes"
            Resource:
              - !GetAtt SQSBusEventsQueue.Arn
              - !GetAtt SQSBusEventsDeadLetterQueue.Arn

  ActivityLogDynamoTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: activity-log
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: N
        - AttributeName: gsi1pk
          AttributeType: S
        - AttributeName: gsi1sk
          AttributeType: N
        - AttributeName: gsi2pk
          AttributeType: S
        - AttributeName: gsi2sk
          AttributeType: S
        - AttributeName: gsi3pk
          AttributeType: S
        - AttributeName: gsi3sk
          AttributeType: S
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      SSESpecification:
        SSEEnabled: true
      DeletionProtectionEnabled: false
      GlobalSecondaryIndexes:
        - IndexName: GSI1
          KeySchema:
            - AttributeName: gsi1pk
              KeyType: HASH
            - AttributeName: gsi1sk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: GSI2
          KeySchema:
            - AttributeName: gsi2pk
              KeyType: HASH
            - AttributeName: gsi2sk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: GSI3
          KeySchema:
            - AttributeName: gsi3pk
              KeyType: HASH
            - AttributeName: gsi3sk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
  #----------------------------------------------------------------
  # API Gateway processing of events
  #----------------------------------------------------------------
  ApiGateway:
    Type: AWS::Serverless::HttpApi
    Properties:
      Name: !Ref ServiceName
      StageName: live

  # The lambda function that will process Api Gateway requests
  LambdaApiFunction:
    Type: AWS::Serverless::Function
    DependsOn: LambdaFunctionRolePolicy
    Properties:
      FunctionName: !Sub ${ServiceName}-api
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 27
      MemorySize: 384
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      AutoPublishAlias: live
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: api-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          ACTIVITY_LOG_TABLE_NAME: !Ref ActivityLogDynamoTable
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
      Events:
        DefaultEventSource:
          Type: HttpApi
          Properties:
            ApiId: !Ref ApiGateway

  ApiGatewayApiMapping:
    Type: AWS::ApiGatewayV2::ApiMapping
    DependsOn: ApiGatewayliveStage
    Properties:
      DomainName: !ImportValue MAIN-API-DOMAIN-NAME
      ApiId: !Ref ApiGateway
      ApiMappingKey: api/activity-log
      Stage: live

  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${ServiceName}-api

  #----------------------------------------------------------------
  # SQS Processing of messages from the Main SNS Bus Event
  #----------------------------------------------------------------
  SQSBusEventsDeadLetterQueue:
    Type: AWS::SQS::Queue

  SQSBusEventsDeadLetterQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsDeadLetterQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: sqs:SendMessage
            Resource: !GetAtt SQSBusEventsDeadLetterQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  # SQS Queue for internal events
  SQSBusEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ServiceName}-queue
      VisibilityTimeout: 1500
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt SQSBusEventsDeadLetterQueue.Arn
        maxReceiveCount: 5

  SQSBusEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: "sqs:SendMessage"
            Resource: !GetAtt SQSBusEventsQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  LambdaSQSFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ServiceName}-sqs
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 500
      MemorySize: 384
      AutoPublishAlias: live
      Tracing: Active
      Layers:
        - !Sub "arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2"
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: sqs-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          ACTIVITY_LOG_TABLE_NAME: !Ref ActivityLogDynamoTable
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
      Events:
        SQS:
          Type: SQS
          Properties:
            Queue: !GetAtt SQSBusEventsQueue.Arn
            BatchSize: 10
