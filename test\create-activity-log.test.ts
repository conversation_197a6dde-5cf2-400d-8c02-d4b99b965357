import { app } from "../src/api-handler";
import request from "supertest";
import { ActivityLogRequest } from "@mainframe-peru/types/build/common";
import { TestData, userAuthToken } from "./common";
import { sc } from "../src/services";

describe("create activity log tests", () => {
  test("Create a new activity log", async () => {
    const entity: ActivityLogRequest = {
      type: "UPDATE_ACCOUNT",
      status: "SUCCESS",
      timestamp: new Date(),
      message: "Test message",
      request: "{ id: 123 }",
    };

    const response = await request(app)
      .post("/live/")
      .send(entity)
      .set("Cookie", `session=${await userAuthToken}`);
    expect(response.statusCode).toEqual(204);

    const activityLog = (
      await sc.db.get({
        TableName: sc.vars.activityLogTableName,
        Key: {
          pk: "u#" + TestData.user.id,
          sk: entity.timestamp.getTime(),
        },
      })
    ).Item;

    expect(activityLog).toEqual(
      expect.objectContaining({
        pk: "u#" + TestData.user.id,
        sk: entity.timestamp.getTime(),
        clientType: "u",
        clientId: TestData.user.id,
        influencerId: TestData.influencer.id,
        type: entity.type,
        status: entity.status,
        message: entity.message,
        request: entity.request,
        createdAt: entity.timestamp.getTime(),
        gsi1pk: expect.any(String),
        gsi1sk: expect.any(Number),
        gsi2pk: expect.any(String),
        gsi2sk: expect.any(String),
      }),
    );
  });
});
