import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { sc } from "../services";
import { createActivityLogMiddleware } from "./create-activity-log";
import { listActivityLogMiddleware } from "./list-activity-log";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Endpoint for users to register activity log
// Secured for API calls
router.post("/", authMiddleware, createActivityLogMiddleware);
// Open for SQS messages
router.post("/create", createActivityLogMiddleware);

router.get("/", authMiddleware, listActivityLogMiddleware);

export const activityLogRouter = router;

