import { errorMiddleware, loggerMiddleware } from "@mainframe-peru/common-core";
import Koa from "koa";
import Router from "@koa/router";
import { bodyParser } from "@koa/bodyparser";
import serverlessExpress from "@codegenie/serverless-express";
import { activityLogRouter } from "./http";
import { createActivityLogMiddleware } from "./http/create-activity-log";
import { listActivityLogMiddleware } from "./http/list-activity-log";

const koa = new Koa();
const router = new Router({
  prefix: "/live",
});

/**
 * Middlewares
 */
koa.use(errorMiddleware);
koa.use(bodyParser());
koa.use(loggerMiddleware);

/**
 * Setup routes
 */
router.use("/", activityLogRouter.routes());
router.post("/activity-log", createActivityLogMiddleware);
router.get("/", listActivityLogMiddleware);

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = serverlessExpress({ app });

