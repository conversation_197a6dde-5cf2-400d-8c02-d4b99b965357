const yaml = require("js-yaml");
const fs = require("fs");
const { CLOUDFORMATION_SCHEMA } = require("cloudformation-js-yaml-schema");

module.exports = async () => {
  const cf = yaml.load(fs.readFileSync("./aws-sam-template.yaml", "utf8"), {
    schema: CLOUDFORMATION_SCHEMA,
  });

  const tables = Object.keys(cf.Resources)
    .filter((item) => cf.Resources[item].Type === "AWS::DynamoDB::Table")
    .map((item) => ({
      ...cf.Resources[item].Properties,
      DeletionProtectionEnabled: false,
    }));

  return {
    tables,
    port: 8000,
  };
};
