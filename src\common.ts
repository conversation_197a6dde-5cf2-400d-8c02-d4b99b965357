import { pchujoy } from "@mainframe-peru/types";
import { sc } from "./services";
import { PublishCommand, PublishCommandOutput } from "@aws-sdk/client-sns";
import { logger } from "@mainframe-peru/common-core";

export const pchujoyBackendQueue = {
  async sendMessage(
    request: pchujoy.sqs.EvaluateAction,
  ): Promise<PublishCommandOutput | undefined> {
    try {
      const command = new PublishCommand({
        TopicArn: sc.vars.snsTopicArn,
        Message: JSON.stringify(request),
        MessageAttributes: {
          service: {
            DataType: "String",
            StringValue: "pchujoy-backend",
          },
          path: {
            DataType: "String",
            StringValue: "/season-pass-mission/evaluate-action",
          },
          method: {
            DataType: "String",
            StringValue: "POST",
          },
          origin: {
            DataType: "String",
            StringValue: "activity-log",
          },
        },
      });
      return await sc.sns.send(command);
    } catch (e) {
      logger.error("Error sending message to sqs email queue", e);
    }
  },
};
