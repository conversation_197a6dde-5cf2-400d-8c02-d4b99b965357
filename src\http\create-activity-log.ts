import { Middleware } from "@koa/router";
import { ActivityLogRequestSchema } from "@mainframe-peru/types/build/common";
import { sc } from "../services";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoyBackendQueue } from "../common";

const clientTypePrefix = (clientType: string) => {
  if (clientType.includes("user")) return "u";
  if (clientType.includes("admin")) return "a";
  return "o";
};

export const createActivityLogMiddleware: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  unknown
> = async ({ request, state, response }) => {
  try {
    const body = ActivityLogRequestSchema.parse(request.body);

    const clientType = clientTypePrefix(
      state.auth ? state.auth.iss : (body.clientType as string),
    );
    const clientId = state.auth ? state.auth.id : (body.clientId as number);
    const influencerId = state.auth
      ? state.auth.iss === "mainframe:officer"
        ? "mainframe"
        : state.auth.influencerId
      : body.influencerId;

    const timestamp = body.timestamp.getTime();

    const entity = {
      pk: `${clientType}#${clientId}`,
      sk: timestamp,
      clientType,
      clientId,
      influencerId,
      type: body.type,
      status: body.status,
      message: body.message,
      request: body.request,
      createdAt: timestamp,

      gsi1pk: influencerId,
      gsi1sk: timestamp,

      gsi2pk: `${clientType}#${clientId}`,
      gsi2sk: `${body.type}#${body.status}#${timestamp}`,

      gsi3pk: influencerId,
      gsi3sk: `${body.type}#${body.status}#${timestamp}`,
    };

    await sc.db.put({
      TableName: sc.vars.activityLogTableName,
      Item: entity,
    });

    if (influencerId === "pchujoy") {
      await pchujoyBackendQueue.sendMessage({
        action: body.type,
        userId: clientId,
      });
    }
  } catch (error) {
    console.log(JSON.stringify(error));
  }
  response.body = null;
};